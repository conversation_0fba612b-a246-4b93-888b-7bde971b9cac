/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c5aa0;
}

.logo i {
    margin-right: 10px;
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #2c5aa0;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 5px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu a {
    display: block;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    display: flex;
    align-items: center;
    min-height: 80vh;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #2c5aa0;
    color: white;
}

.btn-primary:hover {
    background: #1e3f73;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #2c5aa0;
}

.btn-outline {
    background: transparent;
    color: #2c5aa0;
    border: 2px solid #2c5aa0;
}

.btn-outline:hover {
    background: #2c5aa0;
    color: white;
}

.btn-whatsapp {
    background: #25d366;
    color: white;
}

.btn-whatsapp:hover {
    background: #1da851;
    transform: translateY(-2px);
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #f8f9fa;
}

.features h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card i {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* Products Overview */
.products-overview {
    padding: 80px 0;
}

.products-overview h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.product-category {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.product-category:hover {
    transform: translateY(-5px);
}

.product-category img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.category-content {
    padding: 1.5rem;
}

.category-content h3 {
    margin-bottom: 1rem;
    color: #333;
}

.category-content p {
    margin-bottom: 1.5rem;
    color: #666;
}

/* Contact CTA */
.contact-cta {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.contact-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.contact-cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #2c5aa0;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: #2c5aa0;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #2c5aa0;
    border-radius: 50%;
    transition: background 0.3s;
}

.social-links a:hover {
    background: #1e3f73;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
    z-index: 1000;
    transition: all 0.3s;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6);
}

.whatsapp-float i {
    font-size: 1.8rem;
    color: white;
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Product Categories */
.product-categories {
    padding: 80px 0;
    background: #f8f9fa;
}

.product-categories h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.category-item:hover {
    transform: translateY(-5px);
}

.category-item i {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.category-item h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* Product Showcase */
.product-showcase {
    padding: 80px 0;
}

.product-showcase h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.product-info {
    padding: 1.5rem;
}

.product-info h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

.product-code {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.product-features {
    list-style: none;
    margin: 1rem 0;
}

.product-features li {
    padding: 0.25rem 0;
    color: #666;
}

.product-features li:before {
    content: "✓";
    color: #2c5aa0;
    font-weight: bold;
    margin-right: 0.5rem;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.product-actions .btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

/* Services Section */
.services-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.services-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card i {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.service-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.service-card p {
    margin-bottom: 1.5rem;
    color: #666;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .features-grid,
    .products-grid,
    .category-grid,
    .services-grid {
        grid-template-columns: 1fr;
    }

    .hero-buttons,
    .cta-buttons,
    .header-actions {
        justify-content: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .product-actions {
        flex-direction: column;
    }

    .product-actions .btn {
        flex: none;
    }
}

/* Contact Page Styles */
.contact-methods {
    padding: 80px 0;
    background: #f8f9fa;
}

.contact-methods h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-method {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.contact-method:hover {
    transform: translateY(-5px);
}

.contact-method i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.contact-method .fa-whatsapp {
    color: #25d366;
}

.contact-method .fa-phone {
    color: #2c5aa0;
}

.contact-method .fa-envelope {
    color: #e74c3c;
}

.contact-method .fa-clock {
    color: #f39c12;
}

.contact-method h3 {
    margin-bottom: 1rem;
    color: #333;
}

.contact-method p {
    margin-bottom: 1.5rem;
    color: #666;
}

/* Contact Form */
.contact-form-section {
    padding: 80px 0;
}

.form-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.form-content h2 {
    margin-bottom: 1rem;
    color: #333;
}

.form-content p {
    margin-bottom: 2rem;
    color: #666;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c5aa0;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.form-actions .btn {
    flex: 1;
}

/* Contact Info Card */
.contact-info-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: fit-content;
}

.contact-info-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.info-item i {
    font-size: 1.5rem;
    color: #2c5aa0;
    margin-top: 0.25rem;
}

.info-item h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.info-item p {
    color: #666;
    line-height: 1.5;
}

.quick-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
}

.quick-actions h4 {
    margin-bottom: 1rem;
    color: #333;
}

.quick-actions .btn {
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
    text-align: center;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Location Section */
.location-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.location-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.map-container {
    margin-bottom: 2rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.location-info {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.location-details {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.location-details h3 {
    margin-bottom: 1rem;
    color: #333;
}

.location-details ul {
    list-style: none;
    margin: 1rem 0;
}

.location-details li {
    padding: 0.25rem 0;
    color: #666;
}

.location-details li:before {
    content: "✓";
    color: #2c5aa0;
    font-weight: bold;
    margin-right: 0.5rem;
}

.directions {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.directions h4 {
    margin-bottom: 1rem;
    color: #333;
}

.directions p {
    margin-bottom: 0.5rem;
    color: #666;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
}

.faq-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.faq-item h4 {
    margin-bottom: 1rem;
    color: #333;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

/* About Page Styles */
.company-overview {
    padding: 80px 0;
}

.overview-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
}

.overview-text h2 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 2.5rem;
}

.overview-text p {
    margin-bottom: 1.5rem;
    color: #666;
    line-height: 1.6;
    font-size: 1.1rem;
}

.company-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat-item h3 {
    font-size: 2.5rem;
    color: #2c5aa0;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #666;
    font-weight: 600;
}

.overview-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Mission & Vision */
.mission-vision {
    padding: 80px 0;
    background: #f8f9fa;
}

.mv-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.mv-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.mv-card:hover {
    transform: translateY(-5px);
}

.mv-card i {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.mv-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.mv-card p {
    color: #666;
    line-height: 1.6;
}

/* Certifications */
.certifications {
    padding: 80px 0;
}

.certifications h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.cert-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.cert-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.cert-item:hover {
    transform: translateY(-3px);
}

.cert-item i {
    font-size: 2.5rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.cert-item h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.cert-item p {
    color: #666;
    font-size: 0.9rem;
}

/* Team Section */
.team-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.team-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.team-member {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-image {
    width: 80px;
    height: 80px;
    background: #2c5aa0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.member-image i {
    font-size: 2rem;
    color: white;
}

.team-member h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.position {
    color: #2c5aa0;
    font-weight: 600;
    margin-bottom: 1rem;
}

.team-member p {
    color: #666;
    line-height: 1.5;
}

/* Why Choose Us */
.why-choose-us {
    padding: 80px 0;
}

.why-choose-us h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.reasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.reason-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.reason-item:hover {
    transform: translateY(-5px);
}

.reason-item i {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.reason-item h4 {
    margin-bottom: 1rem;
    color: #333;
}

.reason-item p {
    color: #666;
    line-height: 1.6;
}

/* Testimonials */
.testimonials {
    padding: 80px 0;
    background: #f8f9fa;
}

.testimonials h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.testimonial-item:hover {
    transform: translateY(-5px);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    color: #666;
    line-height: 1.6;
    font-style: italic;
    position: relative;
}

.testimonial-content p:before {
    content: '"';
    font-size: 3rem;
    color: #2c5aa0;
    position: absolute;
    left: -20px;
    top: -10px;
}

.testimonial-author h5 {
    color: #333;
    margin-bottom: 0.25rem;
}

.testimonial-author span {
    color: #2c5aa0;
    font-size: 0.9rem;
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
    .overview-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .company-stats {
        grid-template-columns: 1fr 1fr;
    }

    .mv-grid,
    .cert-grid,
    .team-grid,
    .reasons-grid,
    .testimonial-grid {
        grid-template-columns: 1fr;
    }

    .form-container {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .location-info {
        grid-template-columns: 1fr;
    }

    .contact-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* Coming Soon Section */
.coming-soon {
    padding: 120px 0;
    text-align: center;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.coming-soon-content {
    max-width: 600px;
    margin: 0 auto;
}

.coming-soon-content i {
    font-size: 5rem;
    color: #2c5aa0;
    margin-bottom: 2rem;
}

.coming-soon-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.coming-soon-content p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.contact-options {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .coming-soon-content h2 {
        font-size: 2rem;
    }

    .coming-soon-content i {
        font-size: 4rem;
    }

    .contact-options {
        flex-direction: column;
        align-items: center;
    }

    .contact-options .btn {
        width: 100%;
        max-width: 300px;
    }
}
