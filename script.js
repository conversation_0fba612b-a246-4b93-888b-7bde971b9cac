// WhatsApp Integration
function openWhatsApp(message = '') {
    const phoneNumber = '919876543210'; // Replace with actual WhatsApp business number
    const defaultMessage = message || 'Hello! I am interested in your medical equipment products. Please provide more information.';
    const encodedMessage = encodeURIComponent(defaultMessage);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappURL, '_blank');
}

// Product-specific WhatsApp messages
function openWhatsAppForProduct(productCategory) {
    let message = '';
    switch(productCategory) {
        case 'surgical':
            message = 'Hello! I am interested in your surgical equipment. Please share the catalog and pricing details.';
            break;
        case 'medical-devices':
            message = 'Hello! I would like to know more about your medical devices and monitoring equipment.';
            break;
        case 'sanitary':
            message = 'Hello! I need information about your sanitary and hygiene products for our healthcare facility.';
            break;
        case 'technology':
            message = 'Hello! I am interested in your healthcare technology solutions and digital platforms.';
            break;
        default:
            message = 'Hello! I am interested in your medical equipment products. Please provide more information.';
    }
    openWhatsApp(message);
}

// Contact form WhatsApp integration
function sendWhatsAppFromForm() {
    const name = document.getElementById('name')?.value || '';
    const email = document.getElementById('email')?.value || '';
    const phone = document.getElementById('phone')?.value || '';
    const company = document.getElementById('company')?.value || '';
    const productInterest = document.getElementById('product-interest')?.value || '';
    const messageText = document.getElementById('message')?.value || '';
    
    let whatsappMessage = `Hello! I would like to inquire about your medical equipment.\n\n`;
    whatsappMessage += `Name: ${name}\n`;
    whatsappMessage += `Email: ${email}\n`;
    whatsappMessage += `Phone: ${phone}\n`;
    if (company) whatsappMessage += `Company: ${company}\n`;
    if (productInterest) whatsappMessage += `Product Interest: ${productInterest}\n`;
    if (messageText) whatsappMessage += `Message: ${messageText}\n`;
    whatsappMessage += `\nPlease contact me with more details. Thank you!`;
    
    openWhatsApp(whatsappMessage);
}

// Mobile Navigation Toggle
function toggleMobileNav() {
    const navMenu = document.querySelector('.nav-menu');
    const hamburger = document.querySelector('.hamburger');
    
    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
}

// Smooth Scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    // Add click event to hamburger menu
    const hamburger = document.querySelector('.hamburger');
    if (hamburger) {
        hamburger.addEventListener('click', toggleMobileNav);
    }
    
    // Smooth scrolling for internal links
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add scroll effect to header
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = '#fff';
            header.style.backdropFilter = 'none';
        }
    });
    
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .product-category');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#e74c3c';
            isValid = false;
        } else {
            field.style.borderColor = '#ddd';
        }
    });
    
    // Email validation
    const emailField = form.querySelector('input[type="email"]');
    if (emailField && emailField.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailField.value)) {
            emailField.style.borderColor = '#e74c3c';
            isValid = false;
        }
    }
    
    // Phone validation
    const phoneField = form.querySelector('input[type="tel"]');
    if (phoneField && phoneField.value) {
        const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(phoneField.value)) {
            phoneField.style.borderColor = '#e74c3c';
            isValid = false;
        }
    }
    
    return isValid;
}

// Quick quote functionality
function requestQuickQuote() {
    const message = 'Hello! I would like to request a quick quote for medical equipment. Please contact me with your best prices and availability.';
    openWhatsApp(message);
}

// Emergency contact
function emergencyContact() {
    const message = 'URGENT: I need immediate assistance with medical equipment. Please contact me as soon as possible.';
    openWhatsApp(message);
}

// Product inquiry with details
function inquireProduct(productName, productCode = '') {
    let message = `Hello! I am interested in the following product:\n\n`;
    message += `Product: ${productName}\n`;
    if (productCode) message += `Product Code: ${productCode}\n`;
    message += `\nPlease provide:\n`;
    message += `- Detailed specifications\n`;
    message += `- Pricing information\n`;
    message += `- Availability and delivery time\n`;
    message += `- Installation and training details\n\n`;
    message += `Thank you!`;
    
    openWhatsApp(message);
}

// Bulk order inquiry
function bulkOrderInquiry() {
    const message = 'Hello! I am interested in placing a bulk order for medical equipment. Please contact me to discuss quantities, pricing, and delivery terms.';
    openWhatsApp(message);
}

// Service request
function serviceRequest() {
    const message = 'Hello! I need technical service or maintenance for my medical equipment. Please schedule a service visit. Thank you!';
    openWhatsApp(message);
}

// Training request
function trainingRequest() {
    const message = 'Hello! I would like to request training for our staff on medical equipment operation and maintenance. Please provide details about your training programs.';
    openWhatsApp(message);
}

// Add CSS for mobile navigation
const mobileNavCSS = `
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: white;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 1rem 0;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: #f8f9fa;
        margin-top: 0.5rem;
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
}
`;

// Inject mobile navigation CSS
const style = document.createElement('style');
style.textContent = mobileNavCSS;
document.head.appendChild(style);
