# BharatMedTech Solutions - Medical Equipment Website

A comprehensive website for a medical surgical equipment company with integrated WhatsApp messaging functionality.

## 🏥 About the Project

This website is designed for BharatMedTech Solutions, a medical equipment company specializing in:
- Surgical Equipment
- Medical Devices  
- Sanitary Products
- Technology Solutions

## ✨ Key Features

### 🔧 Core Functionality
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Modern UI/UX**: Professional medical industry design with clean aesthetics
- **Fast Loading**: Optimized images and efficient CSS/JS

### 📱 WhatsApp Integration
- **Floating WhatsApp Button**: Always accessible chat button
- **Product-Specific Messaging**: Tailored WhatsApp messages for different product categories
- **Contact Form Integration**: Send form data directly via WhatsApp
- **Quick Actions**: Emergency contact, bulk orders, service requests
- **Pre-filled Messages**: Context-aware message templates

### 📄 Pages Included
1. **Homepage** (`index.html`) - Company overview and featured products
2. **About Us** (`about.html`) - Company story, team, certifications
3. **Contact** (`contact.html`) - Multiple contact methods and inquiry form
4. **Surgical Equipment** (`surgical-equipment.html`) - Detailed product catalog
5. **Medical Devices** (`medical-devices.html`) - Medical monitoring equipment
6. **Sanitary Products** (`sanitary-products.html`) - Coming soon page
7. **Technology Solutions** (`technology-solutions.html`) - Coming soon page

## 🛠️ Technical Stack

- **HTML5**: Semantic markup and accessibility
- **CSS3**: Modern styling with Flexbox and Grid
- **JavaScript**: Interactive features and WhatsApp integration
- **Font Awesome**: Professional icons
- **Google Fonts**: Typography via CDN

## 📱 WhatsApp Features

### Phone Number Configuration
Update the WhatsApp business number in `script.js`:
```javascript
const phoneNumber = '************'; // Replace with actual number
```

### Message Templates
The website includes pre-configured message templates for:
- General inquiries
- Product-specific questions
- Bulk orders
- Service requests
- Emergency support
- Training requests

### Integration Points
- Floating chat button (bottom-right corner)
- Header call-to-action buttons
- Product inquiry buttons
- Contact form submission
- Emergency contact options

## 🎨 Design Features

### Color Scheme
- Primary Blue: `#2c5aa0`
- Secondary Blue: `#1e3f73`
- WhatsApp Green: `#25d366`
- Background Gray: `#f8f9fa`

### Typography
- Primary Font: Segoe UI, Tahoma, Geneva, Verdana, sans-serif
- Responsive font sizes
- Professional medical industry styling

### Layout
- Mobile-first responsive design
- Grid and Flexbox layouts
- Smooth scrolling navigation
- Hover effects and animations

## 🚀 Getting Started

### Local Development
1. Clone or download the project files
2. Start a local server:
   ```bash
   python3 -m http.server 8000
   ```
3. Open `http://localhost:8000` in your browser

### File Structure
```
BharatMedTechSolutions/
├── index.html              # Homepage
├── about.html              # About page
├── contact.html            # Contact page
├── surgical-equipment.html # Surgical products
├── medical-devices.html    # Medical devices
├── sanitary-products.html  # Sanitary products (coming soon)
├── technology-solutions.html # Technology (coming soon)
├── styles.css              # Main stylesheet
├── script.js               # JavaScript functionality
└── README.md               # This file
```

## 📞 Contact Configuration

### Update Contact Information
Modify these details in all HTML files:
- Phone: `+91 98765 43210`
- Email: `<EMAIL>`
- Address: `Mumbai, Maharashtra, India`
- WhatsApp: `************`

### Social Media Links
Update social media URLs in the footer sections of all pages.

## 🔧 Customization

### Adding New Products
1. Add product cards to the relevant product pages
2. Update the `inquireProduct()` function calls
3. Add product images to the `img` tags

### Modifying WhatsApp Messages
Edit the message templates in `script.js`:
- `openWhatsAppForProduct()` function
- Individual action functions (bulkOrderInquiry, serviceRequest, etc.)

### Styling Changes
- Main styles: `styles.css`
- Color variables can be updated globally
- Responsive breakpoints: 768px for mobile

## 📱 Mobile Optimization

- Hamburger menu for mobile navigation
- Touch-friendly buttons and links
- Optimized images and loading
- Responsive grid layouts
- Mobile-first CSS approach

## 🔒 Security Considerations

- No sensitive data stored in frontend
- WhatsApp integration uses public API
- Form validation included
- No external dependencies except CDN resources

## 🌟 Future Enhancements

- Add more detailed product pages
- Implement search functionality
- Add product comparison features
- Include customer testimonials
- Add blog/news section
- Implement multi-language support

## 📄 License

This project is created for BharatMedTech Solutions. All rights reserved.

## 🤝 Support

For technical support or customization requests, contact the development team.

---

**Note**: Remember to update the WhatsApp phone number and contact details before deploying to production.
